# 数据恢复功能 - 数据库迁移执行指南

## 📋 迁移概述

本次迁移为XPrinter数据恢复功能添加必要的数据库结构，包括：

- 为现有表添加软删除字段
- 创建数据恢复相关的新表
- 添加性能优化索引

## ⚠️ 执行前准备

### 1. 环境要求

- MySQL 5.7+ 或 MySQL 8.0+
- 具有 ALTER TABLE 和 CREATE TABLE 权限
- 建议在业务低峰期执行

### 2. 备份数据库

```bash
# 备份整个数据库
mysqldump -u [username] -p [database_name] > backup_before_migration_$(date +%Y%m%d_%H%M%S).sql

# 或者只备份相关表
mysqldump -u [username] -p [database_name] templet templet_group cloudfile > backup_core_tables_$(date +%Y%m%d_%H%M%S).sql
```

### 3. 检查当前表结构

```sql
-- 检查相关表是否存在
SHOW TABLES LIKE 'templet%';
SHOW TABLES LIKE 'cloudfile';

-- 检查表结构
DESCRIBE templet;
DESCRIBE templet_group;
DESCRIBE cloudfile;
```

## 🚀 执行步骤

### 步骤1：在测试环境执行

```bash
# 连接到测试数据库
mysql -u [username] -p [test_database]

# 执行迁移脚本
source docs/sql/xprinter-data-recovery-v1.0.sql
```

### 步骤2：验证迁移结果

执行脚本后，会自动运行验证查询。检查输出结果：

**验证字段添加**：

```sql
-- 应该看到3行结果，每个表都有deleteTime字段
SELECT TABLE_NAME, COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('templet', 'templet_group', 'cloudfile')
  AND COLUMN_NAME = 'deleteTime';
```

**验证新表创建**：“

```sql
-- 应该看到2行结果
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('data_recovery_logs', 'archive_records');
```

### 步骤3：测试现有功能

1. 启动应用程序
2. 测试模板的增删改查功能
3. 测试文件上传和管理功能
4. 确认没有报错

### 步骤4：重新生成Model类

```java
// 运行模型生成器
com.sandu.xinye.common.GeneratorModel.main()
```

### 步骤5：在生产环境执行

确认测试环境一切正常后，在生产环境重复步骤1-4。

## 🔍 验证清单

### 数据库层面验证

- [ ] `templet` 表包含 `deleteTime` 字段
- [ ] `templet_group` 表包含 `deleteTime` 字段
- [ ] `cloudfile` 表包含 `deleteTime` 字段
- [ ] `data_recovery_logs` 表创建成功
- [ ] `archive_records` 表创建成功
- [ ] 所有索引创建成功

### 应用层面验证

- [ ] 应用程序正常启动
- [ ] 现有模板功能正常
- [ ] 现有文件功能正常
- [ ] 没有数据库连接错误
- [ ] Model类重新生成成功

### 数据完整性验证

```sql
-- 检查现有数据完整性
SELECT COUNT(*) as total_templates FROM templet;
SELECT COUNT(*) as total_groups FROM templet_group;
SELECT COUNT(*) as total_files FROM cloudfile;

-- 检查deleteTime字段默认值
SELECT COUNT(*) as null_delete_time FROM templet WHERE deleteTime IS NULL;
SELECT COUNT(*) as null_delete_time FROM templet_group WHERE deleteTime IS NULL;
SELECT COUNT(*) as null_delete_time FROM cloudfile WHERE deleteTime IS NULL;
```

## 🚨 故障排除

### 常见问题

**问题1：字段已存在错误**

```
ERROR 1060 (42S21): Duplicate column name 'deleteTime'
```

**解决方案**：检查字段是否已经存在，如果存在则跳过该步骤。

**问题2：索引名冲突**

```
ERROR 1061 (42000): Duplicate key name 'idx_templet_delete_time'
```

**解决方案**：删除已存在的索引或使用不同的索引名。

**问题3：权限不足**

```
ERROR 1142 (42000): ALTER command denied
```

**解决方案**：确保数据库用户具有ALTER TABLE权限。

### 回滚方案

如果迁移出现问题，可以执行以下回滚操作：

```sql
-- 回滚脚本
ALTER TABLE `templet` DROP COLUMN IF EXISTS `deleteTime`;
ALTER TABLE `templet_group` DROP COLUMN IF EXISTS `deleteTime`;
ALTER TABLE `cloudfile` DROP COLUMN IF EXISTS `deleteTime`;

DROP TABLE IF EXISTS `data_recovery_logs`;
DROP TABLE IF EXISTS `archive_records`;

-- 删除相关索引（如果需要）
DROP INDEX IF EXISTS `idx_templet_user_deleted_created` ON `templet`;
DROP INDEX IF EXISTS `idx_templet_deleted_range` ON `templet`;
-- ... 其他索引
```

然后从备份恢复数据：

```bash
mysql -u [username] -p [database_name] < backup_before_migration_[timestamp].sql
```

## 📊 性能影响评估

### 预期影响

- **存储空间**：每条记录增加8字节（DATETIME字段）
- **查询性能**：现有查询需要添加 `WHERE deleteTime IS NULL` 条件
- **索引空间**：新增索引会占用额外存储空间

### 性能优化建议

- 在业务低峰期执行迁移
- 迁移后监控查询性能
- 如有必要，调整现有查询的索引策略

## 📝 迁移记录

| 日期       | 环境 | 执行人 | 状态   | 备注 |
| ---------- | ---- | ------ | ------ | ---- |
| 2024-07-18 | 测试 |        | 待执行 |      |

---

**重要提醒**：

1. 务必在测试环境充分验证后再在生产环境执行
2. 保留数据库备份至少7天
3. 迁移完成后及时更新相关文档
4. 通知相关开发人员重新生成Model类
